{% extends "superadmin/base_superadmin.html" %}
{% load i18n %}
{% load static %}

{% block title %}{{ title }} - {% trans "Superadmin" %}{% endblock %}

{% block extra_css %}
{{ block.super }}
<link href="{% static 'css/superadmin-enhanced.css' %}" rel="stylesheet">
{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item active" aria-current="page">{% trans "Dashboard" %}</li>
{% endblock %}

{% block superadmin_content %}
<!-- Welcome Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="superadmin-card dashboard-welcome-card">
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="welcome-icon">
                        <i class="bi bi-speedometer2"></i>
                    </div>
                    <h4 class="mb-0">{% trans "Welcome to the Superadmin Dashboard" %}</h4>
                </div>
                <p class="mb-0 ms-5 ps-3">{% trans "This dashboard provides an overview of the platform's status and pending actions. Use the sidebar to navigate to specific management areas." %}</p>
            </div>
        </div>
    </div>
</div>

<!-- Stats Cards -->
<div class="row mb-4">
    <!-- Pending Company Approvals -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="superadmin-card dashboard-stat-card card-primary h-100">
            <div class="card-body">
                <div class="stat-card-header">
                    <div class="stat-card-icon">
                        <i class="bi bi-building"></i>
                    </div>
                    <div class="stat-card-title">
                        {% trans "PENDING COMPANY APPROVALS" %}
                    </div>
                </div>
                <div class="stat-card-value">
                    {{ pending_company_approvals }}
                </div>
                <div class="stat-card-label">
                    {% trans "companies" %}
                </div>
                <div class="stat-card-action">
                    <a href="{% url 'superadmin:company_list' %}?status=inactive&entity_type=company" class="btn btn-admin-primary btn-sm btn-icon">
                        <i class="bi bi-eye"></i> {% trans "View Inactive Companies" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Community Approvals -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="superadmin-card dashboard-stat-card card-info h-100">
            <div class="card-body">
                <div class="stat-card-header">
                    <div class="stat-card-icon">
                        <i class="bi bi-people"></i>
                    </div>
                    <div class="stat-card-title">
                        {% trans "PENDING COMMUNITY APPROVALS" %}
                    </div>
                </div>
                <div class="stat-card-value">
                    {{ pending_community_approvals }}
                </div>
                <div class="stat-card-label">
                    {% trans "communities" %}
                </div>
                <div class="stat-card-action">
                    <a href="{% url 'superadmin:company_list' %}?status=inactive&entity_type=community" class="btn btn-admin-info btn-sm btn-icon">
                        <i class="bi bi-eye"></i> {% trans "View Inactive Communities" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Company Tier Changes -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="superadmin-card dashboard-stat-card card-warning h-100">
            <div class="card-body">
                <div class="stat-card-header">
                    <div class="stat-card-icon">
                        <i class="bi bi-tag"></i>
                    </div>
                    <div class="stat-card-title">
                        {% trans "PENDING COMPANY TIER CHANGES" %}
                    </div>
                </div>
                <div class="stat-card-value">
                    {{ pending_company_tier_changes }}
                </div>
                <div class="stat-card-label">
                    {% trans "requests" %}
                </div>
                <div class="stat-card-action">
                    <a href="{% url 'superadmin:company_list' %}?tier_pending=true" class="btn btn-admin-warning btn-sm btn-icon">
                        <i class="bi bi-eye"></i> {% trans "View Pending Tiers" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Assistant Tier Changes -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="superadmin-card dashboard-stat-card card-info h-100">
            <div class="card-body">
                <div class="stat-card-header">
                    <div class="stat-card-icon">
                        <i class="bi bi-robot"></i>
                    </div>
                    <div class="stat-card-title">
                        {% trans "PENDING ASSISTANT TIER CHANGES" %}
                    </div>
                </div>
                <div class="stat-card-value">
                    {{ pending_assistant_tier_changes }}
                </div>
                <div class="stat-card-label">
                    {% trans "requests" %}
                </div>
                <div class="stat-card-action">
                    <a href="{% url 'superadmin:assistant_list' %}?tier_pending=true" class="btn btn-admin-info btn-sm btn-icon">
                        <i class="bi bi-eye"></i> {% trans "View Pending Tiers" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Community Assistants -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="superadmin-card dashboard-stat-card card-success h-100">
            <div class="card-body">
                <div class="stat-card-header">
                    <div class="stat-card-icon">
                        <i class="bi bi-people"></i>
                    </div>
                    <div class="stat-card-title">
                        {% trans "COMMUNITY ASSISTANTS" %}
                    </div>
                </div>
                <div class="stat-card-value">
                    {{ community_assistants_count }}
                </div>
                <div class="stat-card-label">
                    {% trans "assistants" %}
                </div>
                <div class="stat-card-action">
                    <a href="{% url 'superadmin:community_assistant_list' %}" class="btn btn-admin-success btn-sm btn-icon">
                        <i class="bi bi-eye"></i> {% trans "View Community Assistants" %}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="superadmin-card dashboard-actions-card">
            <div class="card-header">
                <h5><i class="bi bi-lightning-charge me-2"></i>{% trans "Quick Actions" %}</h5>
            </div>
            <div class="card-body">
                <div class="row quick-actions">
                    <div class="col-md-2 mb-3">
                        <a href="{% url 'superadmin:company_list' %}?entity_type=company" class="dashboard-action-btn">
                            <div class="action-icon">
                                <i class="bi bi-building"></i>
                            </div>
                            <div class="action-text">
                                {% trans "Manage Companies" %}
                            </div>
                        </a>
                    </div>
                    <div class="col-md-2 mb-3">
                        <a href="{% url 'superadmin:company_list' %}?entity_type=community" class="dashboard-action-btn">
                            <div class="action-icon">
                                <i class="bi bi-people"></i>
                            </div>
                            <div class="action-text">
                                {% trans "Manage Communities" %}
                            </div>
                        </a>
                    </div>
                    <div class="col-md-2 mb-3">
                        <a href="{% url 'superadmin:assistant_list' %}" class="dashboard-action-btn">
                            <div class="action-icon">
                                <i class="bi bi-robot"></i>
                            </div>
                            <div class="action-text">
                                {% trans "Manage Assistants" %}
                            </div>
                        </a>
                    </div>
                    <div class="col-md-2 mb-3">
                        <a href="{% url 'superadmin:community_assistant_list' %}" class="dashboard-action-btn">
                            <div class="action-icon">
                                <i class="bi bi-people"></i>
                            </div>
                            <div class="action-text">
                                {% trans "Manage Community Assistants" %}
                            </div>
                        </a>
                    </div>
                    <div class="col-md-2 mb-3">
                        <a href="{% url 'admin:index' %}" class="dashboard-action-btn">
                            <div class="action-icon">
                                <i class="bi bi-gear"></i>
                            </div>
                            <div class="action-text">
                                {% trans "Django Admin" %}
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Information -->
<div class="row">
    <div class="col-12">
        <div class="superadmin-card dashboard-system-card">
            <div class="card-header">
                <h5><i class="bi bi-info-circle me-2"></i>{% trans "System Information" %}</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="system-info-section">
                            <div class="system-info-title">{% trans "Technical Information" %}</div>
                            <div class="system-info-item">
                                <div class="info-label">{% trans "Django Version" %}</div>
                                <div class="info-value">{{ django_version|default:"Unknown" }}</div>
                            </div>
                            <div class="system-info-item">
                                <div class="info-label">{% trans "Python Version" %}</div>
                                <div class="info-value">{{ python_version|default:"Unknown" }}</div>
                            </div>
                            <div class="system-info-item">
                                <div class="info-label">{% trans "Database" %}</div>
                                <div class="info-value">{{ database_engine|default:"Unknown" }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="system-info-section">
                            <div class="system-info-title">{% trans "Platform Statistics" %}</div>
                            <div class="system-info-item">
                                <div class="info-label">{% trans "Total Companies" %}</div>
                                <div class="info-value">{{ total_companies|default:"0" }}</div>
                            </div>
                            <div class="system-info-item">
                                <div class="info-label">{% trans "Total Assistants" %}</div>
                                <div class="info-value">{{ total_assistants|default:"0" }}</div>
                            </div>
                            <div class="system-info-item">
                                <div class="info-label">{% trans "Total Users" %}</div>
                                <div class="info-value">{{ total_users|default:"0" }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock superadmin_content %}
