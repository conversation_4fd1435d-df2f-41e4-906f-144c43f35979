#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to update the site domain in the Django Site framework.
This ensures that activation emails and other site-related URLs use the correct domain.
"""

import os
import sys
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from django.contrib.sites.models import Site

def update_site_domain(new_domain, new_name=None):
    """
    Update the domain and optionally the name of the current site.
    
    Args:
        new_domain (str): The new domain to set
        new_name (str, optional): The new name to set. If None, uses the new domain.
    
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Get the current site
        site = Site.objects.get_current()
        old_domain = site.domain
        old_name = site.name
        
        # Update the site
        site.domain = new_domain
        site.name = new_name if new_name else new_domain
        site.save()
        
        print(f"Site updated successfully!")
        print(f"Old domain: {old_domain}")
        print(f"New domain: {site.domain}")
        print(f"Old name: {old_name}")
        print(f"New name: {site.name}")
        
        return True
    except Exception as e:
        print(f"Error updating site: {e}")
        return False

def main():
    """Main function to update the site domain."""
    # Default domain to use
    default_domain = "24seven.site"
    
    # Get domain from command line arguments if provided
    if len(sys.argv) > 1:
        domain = sys.argv[1]
    else:
        domain = default_domain
    
    # Get name from command line arguments if provided
    name = None
    if len(sys.argv) > 2:
        name = sys.argv[2]
    
    # Update the site domain
    success = update_site_domain(domain, name)
    
    if success:
        print("\nNext steps:")
        print("1. Restart your application to apply the changes")
        print("2. Test the activation emails to ensure they use the correct domain")
    else:
        print("\nFailed to update the site domain. Please check the error message above.")

if __name__ == "__main__":
    main()
