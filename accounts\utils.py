from io import BytesIO
from django.core.files.base import ContentFile
from django.conf import settings

def generate_qr_code(instance, url_path, field_name='qr_code'):
    """
    Generates a QR code for the given instance and URL path,
    and saves it to the specified ImageField.
    Uses the unified QR code design with a black letter 'A' on a white background.
    """
    try:
        # Import the function from utils.qr_generator to ensure consistent design
        from utils.qr_generator import generate_qr_with_a, generate_model_qr_code

        # Use the unified QR code generation function
        success = generate_model_qr_code(instance, url_path, field_name=field_name)

        return success
    except Exception as e:
        print(f"Error generating QR code for {instance}: {e}")
        return False

def generate_company_qr(company):
    """
    Generate a QR code for a company with the unified QR code design.
    Uses a black letter 'A' on a white background that's 10% larger.
    """
    try:
        # Import the function from utils.qr_generator to ensure consistent design
        from utils.qr_generator import generate_qr_with_a

        # Create company URL
        from django.urls import reverse
        from django.contrib.sites.models import Site

        # Get the company detail URL
        url_path = reverse('accounts:company_detail', kwargs={'slug': company.slug})

        # Get current site domain
        current_site = Site.objects.get_current()
        protocol = 'https' if getattr(settings, 'SECURE_SSL_REDIRECT', False) else 'http'
        full_url = f"{protocol}://{current_site.domain}{url_path}"

        # Generate QR code with the unified design
        qr_img = generate_qr_with_a(full_url, letter="A")

        # Save to BytesIO
        temp_buffer = BytesIO()
        qr_img.save(temp_buffer, format='PNG')
        temp_buffer.seek(0)

        # Create a unique filename without extra directory prefix since model has upload_to path
        from django.utils.text import slugify
        filename = f'company_qr_{slugify(company.name)}.png'

        # Return ContentFile for saving to ImageField
        return ContentFile(temp_buffer.getvalue(), name=filename)
    except Exception as e:
        print(f"Error generating company QR code: {e}")
        return None

def format_phone_number(phone_number):
    """Format phone number to consistent format."""
    # Remove all non-digit characters
    digits = ''.join(filter(str.isdigit, phone_number))

    # Handle different formats based on length and country code
    if len(digits) == 10:  # Standard US/local number
        return f"+1 ({digits[:3]}) {digits[3:6]}-{digits[6:]}"
    elif len(digits) > 10:  # International number
        return f"+{digits[:-10]} ({digits[-10:-7]}) {digits[-7:-4]}-{digits[-4:]}"
    return phone_number  # Return original if can't format

def get_gravatar_url(email, size=40, default='mp'):
    """Generate Gravatar URL for an email address."""
    import hashlib
    email_hash = hashlib.md5(email.lower().encode('utf-8')).hexdigest()
    return f"https://www.gravatar.com/avatar/{email_hash}?s={size}&d={default}"

def generate_invite_token():
    """Generate a secure token for team invitations."""
    from django.utils.crypto import get_random_string
    return get_random_string(64)

def get_company_size_display(employee_count):
    """Convert employee count to display format."""
    if employee_count < 10:
        return "1-9 employees"
    elif employee_count < 50:
        return "10-49 employees"
    elif employee_count < 200:
        return "50-199 employees"
    elif employee_count < 1000:
        return "200-999 employees"
    return "1000+ employees"

def sanitize_company_name(name):
    """Clean and validate company name."""
    from django.utils.text import slugify
    # Remove any special characters except spaces and hyphens
    clean_name = ''.join(c for c in name if c.isalnum() or c in ' -')
    # Convert to title case and strip whitespace
    return clean_name.title().strip()

def validate_domain(domain):
    """Validate a custom domain."""
    import re
    pattern = r'^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$'
    return bool(re.match(pattern, domain))

def calculate_company_metrics(company):
    """Calculate various company metrics."""
    from django.utils import timezone
    from datetime import timedelta

    now = timezone.now()
    last_month = now - timedelta(days=30)

    metrics = {
        'total_members': company.members.count(),
        'active_members': company.members.filter(last_login__gte=last_month).count(),
        'total_content': company.content_set.count(),
        'total_assistants': company.assistants.count(),
        'monthly_activity': company.activity_logs.filter(created_at__gte=last_month).count()
    }

    return metrics


# --- New Permission Checking Utilities (Using django-guardian) ---

# Note: The old get_membership and has_company_permission functions are removed.
# Direct checks using user.has_perm(perm, obj) should be used instead.

from django.contrib.auth.models import Group # Import Group model
from assistants.models import Assistant, AssistantFolder # Import assistant models

def _user_in_company_group(user, company, group_names):
    """Helper function to check if a user is in specified groups for a company."""
    if not user or not user.is_authenticated or not company:
        return False
    try:
        # Assuming group names might be company-specific (e.g., "admin_1", "member_1")
        # or generic ("company administrators", "company members").
        # We'll try both specific and generic names.
        company_specific_group_names = [f"{name}_{company.id}" for name in group_names]
        all_possible_names = group_names + company_specific_group_names

        # Check if the user belongs to any of these groups
        return user.groups.filter(name__in=all_possible_names).exists()
    except Group.DoesNotExist:
        return False # Group doesn't exist
    except Exception as e:
        # Log error for debugging?
        print(f"Error checking group membership for user {user.id} in company {company.id}: {e}")
        return False

def can_access_assistant(user, assistant):
    """
    Checks if a user can access (view/use) a specific assistant using django-guardian.

    Args:
        user: The user object.
        assistant: The assistant object.

    Returns:
        bool: True if the user can access the assistant, False otherwise.
    """
    if not user or not user.is_authenticated or not assistant or not assistant.company:
        return False

    # Owner always has access
    if user.id == assistant.company.owner_id:
        return True

    # Public assistants are accessible to any authenticated user
    if assistant.is_public:
        return True

    # For private assistants, check object-level view permission
    # This covers owner, admin (via access_all_private), and folder-specific access
    # granted during the migration.
    if user.has_perm('assistants.view_assistant', assistant):
        return True

    # NEW: Check if user is in company member or admin group for the assistant's company
    company_groups = ["company administrators", "company members"]
    if _user_in_company_group(user, assistant.company, company_groups):
        return True

    return False # Deny access if none of the above conditions are met


def can_edit_assistant(user, assistant):
    """Checks if user can edit a specific assistant using django-guardian."""
    if not user or not user.is_authenticated or not assistant:
        return False

    # Check object-level change permission.
    # This covers owner, admin (via access_all_private), folder-specific access,
    # and potentially creator (if granted change_assistant perm on creation).
    return user.has_perm('assistants.change_assistant', assistant)


def can_delete_assistant(user, assistant):
    """Checks if user can delete a specific assistant using django-guardian."""
    if not user or not user.is_authenticated or not assistant:
        return False
    return user.has_perm('assistants.delete_assistant', assistant)


def can_view_assistant_usage(user, assistant):
    """Checks if user can view usage for a specific assistant using django-guardian."""
    if not user or not user.is_authenticated or not assistant:
        return False
    # Check custom object-level permission
    return user.has_perm('assistants.view_assistant_usage', assistant)


def can_view_assistant_analytics(user, assistant):
    """Checks if user can view analytics for a specific assistant using django-guardian."""
    if not user or not user.is_authenticated or not assistant:
        return False
    # Check custom object-level permission
    return user.has_perm('assistants.view_assistant_analytics', assistant)


def can_create_assistant_token(user, assistant):
    """Checks if user can create tokens for a specific assistant using django-guardian."""
    if not user or not user.is_authenticated or not assistant:
        return False
    # Check custom object-level permission
    return user.has_perm('assistants.create_assistant_token', assistant)

# Note: Permissions related to managing folders (add/change/delete AssistantFolder)
# will typically be checked against the *Company* object, as folders belong to a company.
# e.g., user.has_perm('assistants.add_assistantfolder', company)

# Note: Permissions related to managing company members/settings/billing
# will be checked against the *Company* object.
# e.g., user.has_perm('accounts.manage_members', company)

# --- Category Handling Utilities ---

def handle_categories_from_session(request, company, form):
    """Handle categories from session for company forms."""
    from directory.models import CompanyListing, CompanyCategory

    # Get categories from CompanyListing and convert to comma-separated string
    try:
        listing = CompanyListing.objects.get(company=company)
        categories = listing.categories.all()
        categories_text = ', '.join([category.name for category in categories])
        # Set the initial value for the categories field
        form.initial['categories'] = categories_text
    except CompanyListing.DoesNotExist:
        # If no listing exists, try to get categories from session
        categories_text = request.session.get('company_categories')
        if categories_text:
            form.initial['categories'] = categories_text

            # Create a listing with these categories
            listing, _ = CompanyListing.objects.get_or_create(company=company)

            # Clear existing categories
            listing.categories.clear()

            # Create or get category objects and add them to the listing
            category_names = [name.strip() for name in categories_text.split(',') if name.strip()]
            for name in category_names:
                category, _ = CompanyCategory.objects.get_or_create(name=name)
                listing.categories.add(category)

    return form

def save_categories_to_session(request, form):
    """Save categories from form to session."""
    categories_text = form.cleaned_data.get('categories')
    if categories_text:
        request.session['company_categories'] = categories_text
    return categories_text

def process_categories(request, company, form, save_to_session=True):
    """Process categories from form and save to database and optionally to session."""
    from directory.models import CompanyListing, CompanyCategory

    categories_text = form.cleaned_data.get('categories')
    if categories_text:
        # Get or create listing
        listing, _ = CompanyListing.objects.get_or_create(company=company)

        # Clear existing categories
        listing.categories.clear()

        # Create or get category objects and add them to the listing
        category_names = [name.strip() for name in categories_text.split(',') if name.strip()]
        for name in category_names:
            category, _ = CompanyCategory.objects.get_or_create(name=name)
            listing.categories.add(category)

        # Save to session if requested
        if save_to_session:
            request.session['company_categories'] = categories_text

    return categories_text

# --- Django-Impersonate Utility Functions ---

def get_impersonation_users(request):
    """
    Custom queryset for django-impersonate to determine which users can be impersonated.
    This function is referenced in settings via IMPERSONATE_CUSTOM_USER_QUERYSET.

    Returns all users that are company owners or have specific roles.
    """
    from django.contrib.auth import get_user_model
    from accounts.models import Company

    User = get_user_model()

    # Get all company owners
    company_owners = Company.objects.values_list('owner_id', flat=True).distinct()

    # Return queryset of users who are company owners
    return User.objects.filter(id__in=company_owners)

def allow_impersonation(request, impersonator, impersonatee):
    """
    Custom permission check for django-impersonate.
    This function is referenced in settings via IMPERSONATE_CUSTOM_ALLOW.

    Args:
        request: The current request
        impersonator: The user doing the impersonation
        impersonatee: The user being impersonated

    Returns:
        bool: True if impersonation is allowed, False otherwise
    """
    # Only superusers can impersonate
    if not impersonator.is_superuser:
        return False

    # Check if impersonatee is a company owner
    from accounts.models import Company
    is_company_owner = Company.objects.filter(owner=impersonatee).exists()

    # Allow impersonation if the user is a company owner
    return is_company_owner
