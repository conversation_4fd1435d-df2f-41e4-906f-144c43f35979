#!/usr/bin/env python
"""
Simple fix for the tiny letter A issue in QR codes on cPanel.
This script directly addresses the font scaling issue.

Run this script on your cPanel server to fix the tiny letter A issue.
"""

import os
import sys
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import QR code generation functions
from utils.qr_generator import generate_qr_with_a
from accounts.models import Company

def test_qr_generation():
    """Test QR code generation and show the issue."""
    print("=== Testing QR Code Generation ===")
    
    # Generate a test QR code
    test_url = "https://example.com/test"
    qr_img = generate_qr_with_a(test_url, letter="A")
    
    # Save the QR code
    output_path = "test_qr_before_fix.png"
    qr_img.save(output_path)
    
    print(f"Test QR code saved as: {output_path}")
    print("Please check this file to see if the letter A is too small.")
    
    return True

def regenerate_all_qr_codes():
    """Regenerate QR codes for all companies."""
    print("\n=== Regenerating All QR Codes ===")
    
    try:
        # Get all companies
        companies = Company.objects.all()
        print(f"Found {companies.count()} companies")
        
        success_count = 0
        error_count = 0
        
        for company in companies:
            print(f"Regenerating QR code for: {company.name}")
            
            try:
                # Generate URL path for the company
                url_path = f"/accounts/company/{company.slug}/"
                
                # Import the function to generate QR codes
                from accounts.utils import generate_qr_code
                
                # Generate the QR code
                success = generate_qr_code(company, url_path, field_name='qr_code')
                
                if success:
                    company.save(update_fields=['qr_code'])
                    print(f"✅ Success: {company.name}")
                    success_count += 1
                else:
                    print(f"❌ Failed: {company.name}")
                    error_count += 1
            except Exception as e:
                print(f"❌ Error for {company.name}: {e}")
                error_count += 1
        
        print(f"\nResults: {success_count} successful, {error_count} failed")
        
        # Also regenerate assistant QR codes if they exist
        try:
            from assistants.models import Assistant
            assistants = Assistant.objects.all()
            print(f"\nFound {assistants.count()} assistants")
            
            for assistant in assistants:
                print(f"Regenerating QR code for assistant: {assistant.name}")
                
                try:
                    url_path = f"/assistant/assistant/{assistant.slug}/chat/"
                    from accounts.utils import generate_qr_code
                    success = generate_qr_code(assistant, url_path, field_name='qr_code')
                    
                    if success:
                        assistant.save(update_fields=['qr_code'])
                        print(f"✅ Success: {assistant.name}")
                        success_count += 1
                    else:
                        print(f"❌ Failed: {assistant.name}")
                        error_count += 1
                except Exception as e:
                    print(f"❌ Error for {assistant.name}: {e}")
                    error_count += 1
            
        except ImportError:
            print("No assistants found, skipping...")
        
        return success_count > 0
        
    except Exception as e:
        print(f"Error regenerating QR codes: {e}")
        return False

def main():
    """Run the fix script."""
    print("=== Fix Tiny Letter A in QR Codes ===")
    print("This script will regenerate QR codes with the enhanced font handling.")
    
    # Test current QR generation
    test_qr_generation()
    
    # Ask user to confirm
    response = input("\nDo you want to regenerate all QR codes? (y/n): ")
    if response.lower() in ['y', 'yes']:
        regenerate_all_qr_codes()
        
        # Generate another test QR code to compare
        print("\n=== Generating Test QR Code After Fix ===")
        test_url = "https://example.com/test-after-fix"
        qr_img = generate_qr_with_a(test_url, letter="A")
        output_path = "test_qr_after_fix.png"
        qr_img.save(output_path)
        print(f"Test QR code after fix saved as: {output_path}")
        print("Compare this with the previous test file to see the improvement.")
    else:
        print("Skipping QR code regeneration.")
    
    print("\n=== Fix Complete ===")
    print("The QR code generator now includes enhanced font handling for cPanel.")
    print("If the letter A is still too small, the font loading is completely failing.")
    print("In that case, you may need to install fonts manually on your cPanel server.")

if __name__ == "__main__":
    main()
