#!/usr/bin/env python
"""
Comprehensive Test Suite for QR Code Generation

This script provides comprehensive testing for the QR code generation functionality
in utils/qr_generator.py. It tests various scenarios including:
- Basic QR code generation with letter A
- Different letters and characters
- Font loading and fallback mechanisms
- Custom letter creation when fonts fail
- Image dimensions and positioning
- Error handling and edge cases

Usage:
    python comprehensive_qr_test.py
"""

import os
import sys
import django
from PIL import Image
import traceback

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from utils.qr_generator import (
    generate_qr_with_a, 
    generate_qr_content_file,
    load_font,
    get_text_dimensions,
    QR_LETTER_VERTICAL_OFFSET
)

def test_basic_qr_generation():
    """Test basic QR code generation with letter A"""
    print("\n" + "="*60)
    print("TEST 1: Basic QR Code Generation")
    print("="*60)
    
    try:
        test_url = "https://24seven.site/test-basic-qr"
        print(f"Generating QR code for: {test_url}")
        
        qr_img = generate_qr_with_a(test_url, letter="A")
        
        # Save the QR code
        output_file = "test_basic_qr.png"
        qr_img.save(output_file)
        
        # Verify file was created and get info
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file)
            img_width, img_height = qr_img.size
            
            print(f"✅ SUCCESS: QR code generated successfully!")
            print(f"   File: {output_file}")
            print(f"   Size: {file_size} bytes")
            print(f"   Dimensions: {img_width}x{img_height} pixels")
            print(f"   Format: {qr_img.format}")
            print(f"   Mode: {qr_img.mode}")
            return True
        else:
            print("❌ FAILED: QR code file was not created")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        traceback.print_exc()
        return False

def test_different_letters():
    """Test QR code generation with different letters"""
    print("\n" + "="*60)
    print("TEST 2: Different Letters")
    print("="*60)
    
    test_letters = ["A", "B", "C", "X", "Z", "1", "2", "@"]
    test_url = "https://24seven.site/test-letters"
    results = []
    
    for letter in test_letters:
        try:
            print(f"Testing letter: '{letter}'")
            qr_img = generate_qr_with_a(test_url, letter=letter)
            
            output_file = f"test_letter_{letter.replace('@', 'at')}.png"
            qr_img.save(output_file)
            
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                print(f"   ✅ Letter '{letter}': {file_size} bytes")
                results.append(True)
            else:
                print(f"   ❌ Letter '{letter}': File not created")
                results.append(False)
                
        except Exception as e:
            print(f"   ❌ Letter '{letter}': Error - {e}")
            results.append(False)
    
    success_count = sum(results)
    total_count = len(results)
    print(f"\nResults: {success_count}/{total_count} letters successful")
    return success_count == total_count

def test_font_loading():
    """Test font loading functionality"""
    print("\n" + "="*60)
    print("TEST 3: Font Loading")
    print("="*60)
    
    font_tests = [
        ("Arial Black", 50),
        ("Arial", 50),
        ("Impact", 50),
        ("NonExistentFont", 50),
        ("", 50)  # Empty font name
    ]
    
    results = []
    for font_name, font_size in font_tests:
        try:
            print(f"Testing font: '{font_name}' at size {font_size}")
            font = load_font(font_name, font_size)
            
            if font:
                # Test the font by measuring text
                width, height = get_text_dimensions("A", font)
                print(f"   ✅ Font loaded: Text dimensions {width}x{height}")
                results.append(True)
            else:
                print(f"   ❌ Font loading failed")
                results.append(False)
                
        except Exception as e:
            print(f"   ❌ Font '{font_name}': Error - {e}")
            results.append(False)
    
    success_count = sum(results)
    total_count = len(results)
    print(f"\nFont loading results: {success_count}/{total_count} successful")
    return success_count > 0  # At least one font should work

def test_qr_dimensions():
    """Test QR code dimensions and positioning"""
    print("\n" + "="*60)
    print("TEST 4: QR Code Dimensions and Positioning")
    print("="*60)
    
    try:
        test_url = "https://24seven.site/test-dimensions"
        qr_img = generate_qr_with_a(test_url, letter="A")
        
        width, height = qr_img.size
        print(f"QR Code dimensions: {width}x{height}")
        
        # Calculate expected dimensions based on QR code parameters
        # Version 1 QR code: 21x21 modules + 4 border modules on each side = 29x29 modules
        # With box_size=10: 29 * 10 = 290 pixels
        expected_size = 290
        
        print(f"Expected size: {expected_size}x{expected_size}")
        print(f"Actual size: {width}x{height}")
        
        # Check if dimensions are reasonable (allow some variation)
        if abs(width - expected_size) <= 10 and abs(height - expected_size) <= 10:
            print("✅ Dimensions are within expected range")
            
            # Test center calculation
            center_x, center_y = width // 2, height // 2
            print(f"Calculated center: ({center_x}, {center_y})")
            
            # Test letter size calculations
            black_letter_size = int(min(width, height) * 0.35)
            white_letter_size = int(black_letter_size * 1.1)
            
            print(f"Black letter size: {black_letter_size}")
            print(f"White letter size: {white_letter_size}")
            print(f"Vertical offset: {QR_LETTER_VERTICAL_OFFSET}")
            
            return True
        else:
            print("❌ Dimensions are outside expected range")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        traceback.print_exc()
        return False

def test_content_file_generation():
    """Test QR code content file generation for Django models"""
    print("\n" + "="*60)
    print("TEST 5: Content File Generation")
    print("="*60)
    
    try:
        test_url = "https://24seven.site/test-content-file"
        model_name = "test_model"
        model_slug = "test-slug"
        
        print(f"Generating content file for: {test_url}")
        content_file = generate_qr_content_file(test_url, model_name, model_slug, letter="A")
        
        if content_file:
            print(f"✅ Content file generated successfully")
            print(f"   Filename: {content_file.name}")
            print(f"   Size: {len(content_file.read())} bytes")
            
            # Reset file pointer and save to disk for verification
            content_file.seek(0)
            with open("test_content_file.png", "wb") as f:
                f.write(content_file.read())
            
            print(f"   Saved to: test_content_file.png")
            return True
        else:
            print("❌ Content file generation failed")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        traceback.print_exc()
        return False

def test_error_handling():
    """Test error handling with invalid inputs"""
    print("\n" + "="*60)
    print("TEST 6: Error Handling")
    print("="*60)
    
    test_cases = [
        ("", "A"),  # Empty URL
        ("invalid-url", "A"),  # Invalid URL format
        ("https://24seven.site/test", ""),  # Empty letter
        ("https://24seven.site/test", "ABC"),  # Multiple letters
    ]
    
    results = []
    for url, letter in test_cases:
        try:
            print(f"Testing: URL='{url}', Letter='{letter}'")
            qr_img = generate_qr_with_a(url, letter=letter)
            
            if qr_img:
                print(f"   ✅ Handled gracefully (generated QR code)")
                results.append(True)
            else:
                print(f"   ❌ Failed to generate QR code")
                results.append(False)
                
        except Exception as e:
            print(f"   ⚠️  Exception raised: {e}")
            # For error handling test, exceptions might be expected
            results.append(True)
    
    success_count = sum(results)
    total_count = len(results)
    print(f"\nError handling results: {success_count}/{total_count} cases handled")
    return True  # Error handling test always passes if no crashes

def cleanup_test_files():
    """Clean up test files created during testing"""
    print("\n" + "="*60)
    print("CLEANUP: Removing test files")
    print("="*60)
    
    test_files = [
        "test_basic_qr.png",
        "test_letter_A.png", "test_letter_B.png", "test_letter_C.png",
        "test_letter_X.png", "test_letter_Z.png", "test_letter_1.png",
        "test_letter_2.png", "test_letter_at.png",
        "test_content_file.png"
    ]
    
    removed_count = 0
    for filename in test_files:
        if os.path.exists(filename):
            try:
                os.remove(filename)
                print(f"   Removed: {filename}")
                removed_count += 1
            except Exception as e:
                print(f"   Failed to remove {filename}: {e}")
    
    print(f"Cleaned up {removed_count} test files")

def main():
    """Run all QR code tests"""
    print("🔧 Comprehensive QR Code Generator Test Suite")
    print("=" * 60)
    print("Testing QR code generation functionality...")
    
    # Run all tests
    tests = [
        ("Basic QR Generation", test_basic_qr_generation),
        ("Different Letters", test_different_letters),
        ("Font Loading", test_font_loading),
        ("Dimensions & Positioning", test_qr_dimensions),
        ("Content File Generation", test_content_file_generation),
        ("Error Handling", test_error_handling),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"\n❌ CRITICAL ERROR in {test_name}: {e}")
            traceback.print_exc()
            results.append((test_name, False))
    
    # Print summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! QR code generation is working correctly.")
    elif passed > total // 2:
        print("⚠️  Most tests passed. Some issues detected but core functionality works.")
    else:
        print("❌ Multiple test failures. QR code generation may have issues.")
    
    # Ask user if they want to clean up test files
    try:
        cleanup = input("\nClean up test files? (y/n): ").lower().strip()
        if cleanup in ['y', 'yes']:
            cleanup_test_files()
    except KeyboardInterrupt:
        print("\nTest completed.")

if __name__ == '__main__':
    main()
