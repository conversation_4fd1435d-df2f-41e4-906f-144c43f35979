#!/usr/bin/env python
"""
Final fix for the tiny letter A issue in QR codes on cPanel.
This script addresses the specific issue where QR codes are generated with a very small letter A
instead of the large, bold letter A that should be there.

This happens when font loading fails on cPanel and falls back to the default font.
The enhanced QR generator now includes a custom letter A drawing mechanism as a fallback.

Run this script on your cPanel server to fix the tiny letter A issue.
"""

import os
import sys
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import QR code generation functions
from utils.qr_generator import generate_qr_with_a
from accounts.models import Company

def test_qr_generation():
    """Test QR code generation to see if the fix works."""
    print("=== Testing QR Code Generation ===")
    
    # Generate a test QR code
    test_url = "https://24seven.site/test-qr-code"
    print(f"Generating test QR code for URL: {test_url}")
    
    try:
        qr_img = generate_qr_with_a(test_url, letter="A")
        
        # Save the QR code
        output_path = "test_qr_fixed.png"
        qr_img.save(output_path)
        
        print(f"✅ Test QR code generated successfully!")
        print(f"📁 Saved as: {output_path}")
        print(f"📏 Dimensions: {qr_img.size[0]}x{qr_img.size[1]} pixels")
        
        # Check if the QR code looks correct
        print("\n🔍 Please check the generated QR code:")
        print("   - The letter 'A' should be large and clearly visible in the center")
        print("   - It should be black on a white background")
        print("   - The letter should be properly sized (not tiny)")
        
        return True
    except Exception as e:
        print(f"❌ Error generating test QR code: {e}")
        import traceback
        traceback.print_exc()
        return False

def regenerate_company_qr_codes():
    """Regenerate QR codes for all companies with the fixed generator."""
    print("\n=== Regenerating Company QR Codes ===")
    
    try:
        # Get all companies
        companies = Company.objects.all()
        total_companies = companies.count()
        print(f"Found {total_companies} companies to process")
        
        if total_companies == 0:
            print("No companies found. Please create some companies first.")
            return True
        
        success_count = 0
        error_count = 0
        
        for i, company in enumerate(companies, 1):
            print(f"\n[{i}/{total_companies}] Processing: {company.name}")
            
            try:
                # Generate URL path for the company
                url_path = f"/accounts/company/{company.slug}/"
                
                # Import the function to generate QR codes
                from accounts.utils import generate_qr_code
                
                # Generate the QR code
                success = generate_qr_code(company, url_path, field_name='qr_code')
                
                if success:
                    # Save the company with the new QR code
                    company.save(update_fields=['qr_code'])
                    print(f"   ✅ Success: QR code regenerated")
                    success_count += 1
                else:
                    print(f"   ❌ Failed: QR code generation failed")
                    error_count += 1
            except Exception as e:
                print(f"   ❌ Error: {e}")
                error_count += 1
        
        print(f"\n📊 Results Summary:")
        print(f"   ✅ Successful: {success_count}")
        print(f"   ❌ Failed: {error_count}")
        print(f"   📈 Success Rate: {(success_count/total_companies)*100:.1f}%")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ Error regenerating company QR codes: {e}")
        return False

def regenerate_assistant_qr_codes():
    """Regenerate QR codes for all assistants with the fixed generator."""
    print("\n=== Regenerating Assistant QR Codes ===")
    
    try:
        # Import the Assistant model
        from assistants.models import Assistant
        
        # Get all assistants
        assistants = Assistant.objects.all()
        total_assistants = assistants.count()
        print(f"Found {total_assistants} assistants to process")
        
        if total_assistants == 0:
            print("No assistants found. Skipping assistant QR code regeneration.")
            return True
        
        success_count = 0
        error_count = 0
        
        for i, assistant in enumerate(assistants, 1):
            print(f"\n[{i}/{total_assistants}] Processing: {assistant.name}")
            
            try:
                # Generate URL path for the assistant
                url_path = f"/assistant/assistant/{assistant.slug}/chat/"
                
                # Import the function to generate QR codes
                from accounts.utils import generate_qr_code
                
                # Generate the QR code
                success = generate_qr_code(assistant, url_path, field_name='qr_code')
                
                if success:
                    # Save the assistant with the new QR code
                    assistant.save(update_fields=['qr_code'])
                    print(f"   ✅ Success: QR code regenerated")
                    success_count += 1
                else:
                    print(f"   ❌ Failed: QR code generation failed")
                    error_count += 1
            except Exception as e:
                print(f"   ❌ Error: {e}")
                error_count += 1
        
        print(f"\n📊 Assistant Results Summary:")
        print(f"   ✅ Successful: {success_count}")
        print(f"   ❌ Failed: {error_count}")
        print(f"   📈 Success Rate: {(success_count/total_assistants)*100:.1f}%")
        
        return success_count > 0
        
    except ImportError:
        print("Assistant model not found. Skipping assistant QR code regeneration.")
        return True
    except Exception as e:
        print(f"❌ Error regenerating assistant QR codes: {e}")
        return False

def main():
    """Run the final cPanel QR code fix."""
    print("🔧 === cPanel QR Code Final Fix ===")
    print("This script will fix the tiny letter A issue in QR codes on cPanel.")
    print("The QR generator has been enhanced with a custom letter A fallback mechanism.")
    print()
    
    # Test the current QR generation
    print("Step 1: Testing QR code generation...")
    if not test_qr_generation():
        print("❌ Test failed. Please check the error messages above.")
        return
    
    # Ask user to confirm regeneration
    print("\nStep 2: Regenerate existing QR codes")
    print("This will regenerate QR codes for all companies and assistants.")
    print("The old QR codes will be replaced with new ones using the fixed generator.")
    
    response = input("\nDo you want to proceed with regeneration? (y/n): ").strip().lower()
    
    if response in ['y', 'yes']:
        print("\n🚀 Starting QR code regeneration...")
        
        # Regenerate company QR codes
        company_success = regenerate_company_qr_codes()
        
        # Regenerate assistant QR codes
        assistant_success = regenerate_assistant_qr_codes()
        
        if company_success or assistant_success:
            print("\n🎉 === Fix Complete ===")
            print("✅ QR code regeneration completed successfully!")
            print()
            print("📋 What to do next:")
            print("1. Check the generated test QR code (test_qr_fixed.png)")
            print("2. Verify that the letter 'A' is now properly sized and visible")
            print("3. Test scanning the QR codes to ensure they work correctly")
            print("4. Check a few company/assistant QR codes in your admin interface")
            print()
            print("🔍 If the letter 'A' is still too small:")
            print("- The font loading is completely failing on your server")
            print("- The custom letter A fallback should have activated")
            print("- Contact support if the issue persists")
        else:
            print("\n❌ === Fix Failed ===")
            print("QR code regeneration failed. Please check the error messages above.")
    else:
        print("\n⏭️  Skipping QR code regeneration.")
        print("The QR generator has been enhanced, but existing QR codes were not regenerated.")
        print("New QR codes will use the improved generator automatically.")
    
    print("\n📞 Need help? Contact support with:")
    print("- The output from this script")
    print("- The generated test QR code file")
    print("- Your cPanel server details")

if __name__ == "__main__":
    main()
