"""
Production settings for the company_assistant project.
This module contains settings specific to the production environment.
"""

import os
from pathlib import Path
from .settings import *  # Import base settings
from .cache_settings import DEFAULT_CACHE_CONFIG, SESSION_TOKEN_CACHE_CONFIG

# Set DEBUG to False in production
DEBUG = False

# Set ALLOWED_HOSTS to include your domain
ALLOWED_HOSTS = ['24seven.site', 'www.24seven.site', '*']

# Security settings for production
SECURE_SSL_REDIRECT = True
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True
SECURE_HSTS_SECONDS = 31536000  # 1 year
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True

# CSRF settings
CSRF_COOKIE_HTTPONLY = False  # Allow JavaScript to access the CSRF cookie
CSRF_USE_SESSIONS = False  # Store CSRF token in cookie, not session
CSRF_COOKIE_SAMESITE = 'Lax'  # Allow CSRF cookie to be sent in same-site requests
CSRF_TRUSTED_ORIGINS = ['https://24seven.site', 'http://24seven.site', 'https://www.24seven.site', 'http://www.24seven.site']

# Static files settings with Whitenoise
STATIC_URL = '/static/'
STATIC_ROOT = BASE_DIR / 'staticfiles'
STATICFILES_DIRS = [BASE_DIR / 'static']
STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'

# Whitenoise settings
WHITENOISE_MAX_AGE = 31536000  # 1 year in seconds
WHITENOISE_KEEP_ONLY_HASHED_FILES = True  # Only keep files with hashed names in production

# Add Whitenoise to middleware if not already there
if 'whitenoise.middleware.WhiteNoiseMiddleware' not in MIDDLEWARE:
    # Insert WhiteNoiseMiddleware right after SecurityMiddleware
    security_index = MIDDLEWARE.index('django.middleware.security.SecurityMiddleware')
    MIDDLEWARE.insert(security_index + 1, 'whitenoise.middleware.WhiteNoiseMiddleware')

# Cache settings
CACHES = {
    'default': DEFAULT_CACHE_CONFIG,
    'session_tokens': SESSION_TOKEN_CACHE_CONFIG,
}

# Email settings
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = os.getenv('EMAIL_HOST', 'mail.smartlib.site')
EMAIL_PORT = int(os.getenv('EMAIL_PORT', 465))
EMAIL_USE_SSL = os.getenv('EMAIL_USE_SSL', 'True').lower() == 'true'
EMAIL_USE_TLS = os.getenv('EMAIL_USE_TLS', 'False').lower() == 'true'
EMAIL_HOST_USER = os.getenv('EMAIL_HOST_USER', '<EMAIL>')
EMAIL_HOST_PASSWORD = os.getenv('EMAIL_HOST_PASSWORD', '')
DEFAULT_FROM_EMAIL = os.getenv('DEFAULT_FROM_EMAIL', '24seven <<EMAIL>>')

# Logging settings
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
        'file': {
            'class': 'logging.FileHandler',
            'filename': os.path.join(BASE_DIR, 'logs', 'django.log'),
            'formatter': 'verbose',
        },
        'directory_file': {
            'class': 'logging.FileHandler',
            'filename': os.path.join(BASE_DIR, 'logs', 'directory.log'),
            'formatter': 'verbose',
        },
        'auth_file': {
            'class': 'logging.FileHandler',
            'filename': os.path.join(BASE_DIR, 'logs', 'auth.log'),
            'formatter': 'verbose',
        },
    },
    'root': {
        'handlers': ['console', 'file'],
        'level': 'WARNING',
    },
    'loggers': {
        'django': {
            'handlers': ['console', 'file'],
            'level': os.getenv('DJANGO_LOG_LEVEL', 'INFO'),
            'propagate': False,
        },
        'directory': {
            'handlers': ['console', 'directory_file'],
            'level': 'INFO',
            'propagate': False,
        },
        'accounts.auth_utils': {
            'handlers': ['console', 'auth_file'],
            'level': 'DEBUG',
            'propagate': False,
        },
    },
}

# Create logs directory if it doesn't exist
logs_dir = os.path.join(BASE_DIR, 'logs')
os.makedirs(logs_dir, exist_ok=True)

# Create cache directory if using file-based cache
if DEFAULT_CACHE_CONFIG['BACKEND'] == 'django.core.cache.backends.filebased.FileBasedCache':
    cache_dir = DEFAULT_CACHE_CONFIG['LOCATION']
    os.makedirs(cache_dir, exist_ok=True)

# Set environment variable to indicate we're in a cPanel environment
os.environ['CPANEL_ENV'] = 'True'
