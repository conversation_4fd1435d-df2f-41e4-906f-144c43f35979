#!/usr/bin/env python
"""
Diagnostic script to help identify QR code generation issues on cPanel.
This script will:
1. Generate a QR code with letter A
2. Print detailed information about the font loading process
3. Save the QR code to a file for visual inspection
4. Test with different font fallback options

Run this script on your cPanel server to diagnose QR code generation issues.
"""

import os
import sys
import platform
import django
from PIL import Image, ImageDraw, ImageFont
from io import BytesIO

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import QR code generation functions
from utils.qr_generator import generate_qr_with_a, load_font, QR_LETTER_VERTICAL_OFFSET

def print_system_info():
    """Print system information to help diagnose font issues."""
    print("\n=== System Information ===")
    print(f"Python version: {sys.version}")
    print(f"Platform: {platform.platform()}")
    print(f"OS: {platform.system()}")
    print(f"Machine: {platform.machine()}")
    print(f"Architecture: {platform.architecture()}")
    print(f"Current directory: {os.getcwd()}")
    
    # Check for font directories
    font_dirs = []
    if platform.system() == "Windows":
        font_dirs.append(os.path.join(os.environ.get("WINDIR", "C:\\Windows"), "Fonts"))
        font_dirs.append(os.path.join(os.environ.get("LOCALAPPDATA", ""), "Microsoft", "Windows", "Fonts"))
    elif platform.system() == "Darwin":  # macOS
        font_dirs.append("/Library/Fonts")
        font_dirs.append("/System/Library/Fonts")
        font_dirs.append(os.path.expanduser("~/Library/Fonts"))
    elif platform.system() == "Linux":
        font_dirs.append("/usr/share/fonts/truetype")
        font_dirs.append("/usr/share/fonts/opentype")
        font_dirs.append("/usr/local/share/fonts")
        font_dirs.append(os.path.expanduser("~/.fonts"))
    
    print("\n=== Font Directories ===")
    for font_dir in font_dirs:
        if os.path.exists(font_dir):
            print(f"Found font directory: {font_dir}")
            # List some fonts in this directory (limit to 10)
            try:
                fonts = os.listdir(font_dir)[:10]
                if fonts:
                    print(f"  Sample fonts: {', '.join(fonts)}")
            except Exception as e:
                print(f"  Error listing fonts: {e}")
        else:
            print(f"Font directory not found: {font_dir}")

def test_font_loading():
    """Test font loading with different sizes and names."""
    print("\n=== Font Loading Tests ===")
    
    font_names = [
        "Arial Black",
        "ArialBlack",
        "Arial-Black",
        "arialblk",
        "ariblk",
        "Impact",
        "Arial Bold",
        "arialbd",
        "arial",
        "sans-serif-bold",
        "sans-serif",
        "DejaVuSans-Bold",
        "DejaVuSans",
        "FreeSans",
        "LiberationSans-Bold",
        "LiberationSans"
    ]
    
    font_size = 36
    
    for font_name in font_names:
        try:
            font = load_font(font_name, font_size)
            if font == ImageFont.load_default():
                print(f"Font '{font_name}' not found, using default font")
            else:
                print(f"Successfully loaded font '{font_name}'")
                # Try to get font metrics
                try:
                    test_img = Image.new('RGBA', (100, 100), (0, 0, 0, 0))
                    test_draw = ImageDraw.Draw(test_img)
                    try:
                        # For newer Pillow versions
                        bbox = test_draw.textbbox((0, 0), "A", font=font)
                        width = bbox[2] - bbox[0]
                        height = bbox[3] - bbox[1]
                        print(f"  Font metrics (textbbox): width={width}, height={height}")
                    except AttributeError:
                        # For older Pillow versions
                        width, height = test_draw.textsize("A", font=font)
                        print(f"  Font metrics (textsize): width={width}, height={height}")
                except Exception as e:
                    print(f"  Error getting font metrics: {e}")
        except Exception as e:
            print(f"Error loading font '{font_name}': {e}")

def generate_test_qr_code():
    """Generate a test QR code and save it to a file."""
    print("\n=== Generating Test QR Code ===")
    print(f"Using QR_LETTER_VERTICAL_OFFSET = {QR_LETTER_VERTICAL_OFFSET}")
    
    try:
        # Generate a test QR code
        test_url = "https://example.com/test-qr-code"
        qr_img = generate_qr_with_a(test_url, letter="A")
        
        # Save the QR code
        output_path = "diagnostic_qr_code.png"
        qr_img.save(output_path)
        
        # Get file info
        file_size = os.path.getsize(output_path)
        img_width, img_height = qr_img.size
        
        print(f"QR code generated successfully!")
        print(f"File: {output_path}")
        print(f"Size: {file_size} bytes")
        print(f"Dimensions: {img_width}x{img_height} pixels")
        
        return True
    except Exception as e:
        import traceback
        print(f"Error generating QR code: {e}")
        traceback.print_exc()
        return False

def test_with_different_offsets():
    """Test QR code generation with different vertical offsets."""
    print("\n=== Testing Different Vertical Offsets ===")
    
    offsets = [-60, -50, -40, -30, -20, -10, 0, 10, 20]
    
    for offset in offsets:
        try:
            print(f"\nTesting with offset = {offset}")
            
            # Override the QR_LETTER_VERTICAL_OFFSET for this test
            import utils.qr_generator
            original_offset = utils.qr_generator.QR_LETTER_VERTICAL_OFFSET
            utils.qr_generator.QR_LETTER_VERTICAL_OFFSET = offset
            
            # Generate a test QR code
            test_url = f"https://example.com/test-offset-{offset}"
            qr_img = generate_qr_with_a(test_url, letter="A")
            
            # Save the QR code
            output_path = f"diagnostic_qr_code_offset_{offset}.png"
            qr_img.save(output_path)
            
            print(f"QR code with offset {offset} generated: {output_path}")
            
            # Restore the original offset
            utils.qr_generator.QR_LETTER_VERTICAL_OFFSET = original_offset
            
        except Exception as e:
            print(f"Error testing offset {offset}: {e}")

def main():
    """Run all diagnostic tests."""
    print("=== QR Code Generation Diagnostic Tool ===")
    print("This tool will help diagnose issues with QR code generation on cPanel.")
    
    # Print system information
    print_system_info()
    
    # Test font loading
    test_font_loading()
    
    # Generate a test QR code
    generate_test_qr_code()
    
    # Test with different offsets
    test_with_different_offsets()
    
    print("\n=== Diagnostic Complete ===")
    print("Please check the generated QR code files and the output above for clues about the issue.")
    print("If the letter 'A' is not centered correctly, try adjusting the QR_LETTER_VERTICAL_OFFSET value in utils/qr_generator.py.")

if __name__ == "__main__":
    main()
