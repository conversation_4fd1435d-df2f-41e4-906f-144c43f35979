from pathlib import Path
import os
from django.utils.translation import gettext_lazy as _ # Import gettext_lazy

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings
SECRET_KEY = 'django-insecure-change-this-in-production'
# Set DEBUG based on environment variable, default to True for development
DEBUG = os.environ.get('DEBUG', 'True').lower() != 'false'
# Set ALLOWED_HOSTS to include your domain
ALLOWED_HOSTS = ['*', '24seven.site', 'www.24seven.site']

# Application definition
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.sites', # Added sites framework
    # Third party apps
    'widget_tweaks',
    'crispy_forms', # Added for form rendering
    'crispy_bootstrap5', # Added template pack for crispy-forms
    'guardian', # Added django-guardian
    'impersonate', # Added django-impersonate
    'tinymce', # Added django-tinymce
    # Local apps
    'accounts.apps.AccountsConfig',
    'assistants.apps.AssistantsConfig',
    'content.apps.ContentConfig',
    'directory.apps.DirectoryConfig',
    'site_settings', # Simplified app registration
    'superadmin', # Added superadmin app
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',  # Added Whitenoise middleware
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'accounts.impersonate_debug.ImpersonateDebugMiddleware', # Debug wrapper for impersonate middleware
    'impersonate.middleware.ImpersonateMiddleware', # Added django-impersonate middleware
    'accounts.impersonate_fix.ImpersonateFixMiddleware', # Fix for impersonate middleware
    'accounts.session_cleanup.SessionCleanupMiddleware', # Session cleanup for impersonation
    'accounts.middleware.ImpersonatePermissionsMiddleware', # Custom impersonation middleware
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'company_assistant.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates', # Corrected typo
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'accounts.context_processors.company_context', # Added company context processor
                'accounts.context_processors.impersonation_debug_context', # Added impersonation debug context processor
                'assistants.context_processors.community_assistants_context', # Added community assistants context processor
                'site_settings.context_processors.site_configuration', # Added site configuration context processor
                'directory.context_processors.directory_settings_context', # Added directory settings context processor
            ],
        },
    },
]

WSGI_APPLICATION = 'company_assistant.wsgi.application'

# Database
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'postgres6',  # Changed to the new database
        'USER': 'postgres',  # Your PostgreSQL username
        'PASSWORD': 'M@kerere1',  # Replace with your password
        'HOST': 'localhost',
        'PORT': '5432',
    }
}

# Password validation
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
LANGUAGE_CODE = 'en-us'
LANGUAGES = [
    ('en', _('English')),
    # Add other languages here if needed, e.g.:
    # ('es', _('Spanish')),
    # ('fr', _('French')),
]
TIME_ZONE = 'UTC'
USE_I18N = True
USE_TZ = True

# Static files (CSS, JavaScript, Images)
STATIC_URL = '/static/'
STATIC_ROOT = BASE_DIR / 'staticfiles'
STATICFILES_DIRS = [BASE_DIR / 'static']
# Using Whitenoise for static file serving
STATICFILES_STORAGE = 'whitenoise.storage.CompressedStaticFilesStorage'

# Media files
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

# Default primary key field type
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Authentication settings
LOGIN_URL = 'accounts:login'
LOGIN_REDIRECT_URL = 'home' # Changed from 'accounts:dashboard'
LOGOUT_REDIRECT_URL = 'home'

# Email settings (configured for smartlib.site)
EMAIL_BACKEND = os.getenv('EMAIL_BACKEND', 'django.core.mail.backends.smtp.EmailBackend')
EMAIL_HOST = os.getenv('EMAIL_HOST', 'mail.smartlib.site')
EMAIL_PORT = int(os.getenv('EMAIL_PORT', 465))
EMAIL_USE_SSL = os.getenv('EMAIL_USE_SSL', 'True').lower() == 'true'
EMAIL_USE_TLS = os.getenv('EMAIL_USE_TLS', 'False').lower() == 'true'
EMAIL_HOST_USER = os.getenv('EMAIL_HOST_USER', '<EMAIL>')
EMAIL_HOST_PASSWORD = os.getenv('EMAIL_HOST_PASSWORD', '')
DEFAULT_FROM_EMAIL = os.getenv('DEFAULT_FROM_EMAIL', '24seven <<EMAIL>>')

# Crispy Forms Settings
CRISPY_ALLOWED_TEMPLATE_PACKS = "bootstrap5"
CRISPY_TEMPLATE_PACK = "bootstrap5"

# Site ID (Required for django.contrib.sites)
SITE_ID = 1

# Debug toolbar settings removed

# Custom User Model
# AUTH_USER_MODEL = 'accounts.User' # Removed - Using default Django User model

# Authentication Backends
AUTHENTICATION_BACKENDS = (
    'accounts.backends.EmailOrUsernameModelBackend', # Custom backend for email/username login
    'guardian.backends.ObjectPermissionBackend', # Guardian backend for object permissions
)

# Logging
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
        'file': {
            'class': 'logging.FileHandler',
            'filename': os.path.join(BASE_DIR, 'debug.log'),
            'formatter': 'verbose',
        },
        'directory_file': {
            'class': 'logging.FileHandler',
            'filename': os.path.join(BASE_DIR, 'directory_debug.log'),
            'formatter': 'verbose',
        },
    },
    'root': {
        'handlers': ['console', 'file'],
        'level': 'WARNING',
    },
    'loggers': {
        'django': {
            'handlers': ['console', 'file'],
            'level': os.getenv('DJANGO_LOG_LEVEL', 'INFO'),
            'propagate': False,
        },
        'directory': {
            'handlers': ['console', 'directory_file'],
            'level': 'DEBUG',
            'propagate': False,
        },
    },
}

# File upload settings
FILE_UPLOAD_MAX_MEMORY_SIZE = 5242880  # 5MB
CONTENT_TYPES = ['application/pdf', 'text/plain', 'application/msword',
                 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
MAX_UPLOAD_SIZE = 5242880  # 5MB

# OpenAI settings (configure in local_settings.py or .env)
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY', '')
OPENAI_BASE_URL = os.getenv('OPENAI_BASE_URL', 'https://api.openai.com/v1')
OPENAI_MODEL = 'llama-3.3-70b-versatile'
GEMINI_API_KEY = os.getenv('GEMINI_API_KEY', 'AIzaSyDTjwdVbjGu5Q0v-dP6QsfC4IoKM36g8l8')
# Groq API key for Llama models
GROQ_API_KEY = os.getenv('GROQ_API_KEY', '********************************************************')
#********************************************************, ********************************************************, ********************************************************
GROQ_BASE_URL = os.getenv('GROQ_BASE_URL', 'https://api.groq.com/openai/v1')


# Anthropic settings (configure in local_settings.py or .env)
ANTHROPIC_API_KEY = os.getenv('ANTHROPIC_API_KEY', '')

# QR Code settings
QRCODE_DEFAULTS = {
    'VERSION': 1,
    'ERROR_CORRECTION': 'H',
    'BOX_SIZE': 10,
    'BORDER': 4,
}

# Security settings (explicitly disabled in development)
SECURE_SSL_REDIRECT = False
SESSION_COOKIE_SECURE = False
CSRF_COOKIE_SECURE = False
SECURE_HSTS_SECONDS = 0
SECURE_HSTS_PRELOAD = False

# CSRF settings
CSRF_COOKIE_HTTPONLY = False  # Allow JavaScript to access the CSRF cookie
CSRF_USE_SESSIONS = False  # Store CSRF token in cookie, not session
CSRF_COOKIE_SAMESITE = 'Lax'  # Allow CSRF cookie to be sent in same-site requests
CSRF_TRUSTED_ORIGINS = ['http://localhost:8000', 'http://127.0.0.1:8000', 'https://24seven.site', 'http://24seven.site']  # Updated with production domain

# Cache settings
# Default cache timeout (24 hours in seconds)
CACHE_TIMEOUT = 86400

# File-based cache configuration for cPanel environments
FILE_CACHE_CONFIG = {
    'BACKEND': 'django.core.cache.backends.filebased.FileBasedCache',
    'LOCATION': os.path.join(BASE_DIR, 'cache'),
    'TIMEOUT': CACHE_TIMEOUT,
    'OPTIONS': {
        'MAX_ENTRIES': 1000,
        'CULL_FREQUENCY': 3,  # Fraction of entries to cull when max is reached
    },
}

# Local memory cache configuration for development
LOCMEM_CACHE_CONFIG = {
    'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
    'LOCATION': 'company-assistant-cache',
    'TIMEOUT': CACHE_TIMEOUT,
}

# Determine if we're in a cPanel environment
IN_CPANEL = os.environ.get('CPANEL_ENV') == 'True' or 'PASSENGER_WSGI' in os.environ

# Use file-based cache in cPanel, locmem in development
CACHES = {
    'default': FILE_CACHE_CONFIG if IN_CPANEL else LOCMEM_CACHE_CONFIG,
    'session_tokens': {
        'BACKEND': FILE_CACHE_CONFIG['BACKEND'] if IN_CPANEL else LOCMEM_CACHE_CONFIG['BACKEND'],
        'LOCATION': (FILE_CACHE_CONFIG['LOCATION'] if IN_CPANEL else LOCMEM_CACHE_CONFIG['LOCATION']) + '_tokens',
        'TIMEOUT': CACHE_TIMEOUT,
        'OPTIONS': FILE_CACHE_CONFIG['OPTIONS'] if IN_CPANEL else {},
    },
}

# Session settings
# Use file-based sessions in cPanel, database sessions in development
if IN_CPANEL:
    SESSION_ENGINE = 'django.contrib.sessions.backends.file'
    SESSION_FILE_PATH = os.path.join(BASE_DIR, 'session')
    os.makedirs(SESSION_FILE_PATH, exist_ok=True)
else:
    SESSION_ENGINE = 'django.contrib.sessions.backends.db'

# Increase session cookie age to 2 weeks (in seconds)
SESSION_COOKIE_AGE = 60 * 60 * 24 * 14  # 14 days
SESSION_SAVE_EVERY_REQUEST = True  # Save the session on every request

# Site ID (Required for django.contrib.sites)
SITE_ID = 1

# REMOVED: TinyMCE Configuration

# TinyMCE Configuration
TINYMCE_DEFAULT_CONFIG = {
    'height': 360,
    'width': 'auto',
    'menubar': True,
    'plugins': 'advlist autolink lists link image charmap print preview anchor '
               'searchreplace visualblocks code fullscreen '
               'insertdatetime media table paste code help wordcount imagetools '
               'emoticons hr pagebreak nonbreaking toc textpattern codesample tabfocus', # Enhanced plugins
    'toolbar1': 'formatselect fontselect fontsizeselect | '
                'bold italic underline strikethrough | forecolor backcolor | '
                'alignleft aligncenter alignright alignjustify | '
                'bullist numlist outdent indent',
    'toolbar2': 'undo redo | cut copy paste | searchreplace | '
                'link image media table tableprops tablecellprops tablerowprops tabledelete | '
                'emoticons charmap | hr pagebreak nonbreaking | removeformat code | help', # Enhanced toolbar
    'content_style': '''
        body {
            font-family: Helvetica, Arial, sans-serif;
            font-size: 14px;
            max-width: 100%;
            word-wrap: break-word;
            overflow-wrap: break-word;
            padding: 10px;
            box-sizing: border-box;
        }
        img {
            max-width: 100%;
            height: auto !important;
            display: block;
            margin: 0.5em auto;
            border-radius: 4px;
        }
        p {
            margin: 0 0 1em 0;
            line-height: 1.5;
        }
        /* Table wrapper for horizontal scrolling on small screens */
        .table-wrapper {
            width: 100%;
            overflow-x: auto;
            margin: 0.75rem 0;
            border-radius: 0.5rem;
            position: relative;
            -webkit-overflow-scrolling: touch;
        }
        /* Improved table styling */
        table {
            width: 100% !important;
            max-width: 100% !important;
            margin-bottom: 1rem;
            border-collapse: collapse !important;
            border-spacing: 0 !important;
            overflow: hidden !important;
            table-layout: auto !important; /* Changed from fixed to auto for better content fitting */
        }
        /* Ensure tables don't overflow their containers */
        table td, table th {
            padding: 0.75rem !important;
            vertical-align: top !important;
            border: 1px solid #dee2e6 !important;
            word-break: normal !important;
            overflow-wrap: break-word !important;
            min-width: 50px !important;
        }
        /* Responsive media queries */
        @media (max-width: 992px) {
            body { font-size: 15px; }
            table { font-size: 14px; }
            table td, table th { padding: 0.6rem !important; }
        }
        @media (max-width: 768px) {
            body { font-size: 16px; }
            table { font-size: 14px; border: none !important; }
            table td, table th {
                padding: 0.5rem !important;
                min-width: 80px !important;
            }
            /* Auto-wrap tables in a scrollable container on mobile */
            table:not(.table-wrapper table) {
                display: block;
                width: 100%;
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
            }
        }
        @media (max-width: 576px) {
            body { font-size: 16px; }
            table { font-size: 13px; }
            table td, table th {
                padding: 0.4rem !important;
                min-width: 70px !important;
            }
        }
    ''',
    'image_advtab': True,
    'image_title': True,
    'automatic_uploads': True,
    'file_picker_types': 'image',
    'image_class_list': [
        {'title': 'Responsive', 'value': 'img-fluid'},
        {'title': 'Left Aligned', 'value': 'img-fluid float-start me-3'},
        {'title': 'Right Aligned', 'value': 'img-fluid float-end ms-3'},
        {'title': 'Centered', 'value': 'img-fluid mx-auto d-block'},
    ],
    'image_dimensions': True,
    'image_caption': True,
    'resize_img_proportional': True,
    'object_resizing': 'img,table',
    'resize': True,
    'mobile_friendly': True,
    'paste_data_images': True,
    'table_responsive_width': True,
    'table_default_attributes': {
        'border': '1',
        'cellpadding': '5',
        'cellspacing': '0',
        'class': 'table table-bordered',
    },
    'table_default_styles': {
        'width': '100%',
        'border-collapse': 'collapse',
    },
    'table_appearance_options': False,
    'table_advtab': True,
    'table_cell_advtab': True,
    'table_row_advtab': True,
    'table_class_list': [
        {'title': 'None', 'value': ''},
        {'title': 'Responsive Table', 'value': 'table-responsive'},
        {'title': 'Bordered Table', 'value': 'table-bordered'},
        {'title': 'Striped Table', 'value': 'table-striped'},
        {'title': 'Small Table', 'value': 'table-sm'},
        {'title': 'Hover Effect', 'value': 'table-hover'},
    ],
    'table_grid': True,
    'table_toolbar': 'tableprops tabledelete | tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol',
    'images_upload_url': '/assistants/tinymce/upload/',
    'file_picker_callback': """
        function (callback, value, meta) {
            if (meta.filetype == 'image') {
                var input = document.createElement('input');
                input.setAttribute('type', 'file');
                input.setAttribute('accept', 'image/*');
                input.onchange = function () {
                    var file = this.files[0];
                    var reader = new FileReader();
                    reader.onload = function () {
                        callback(reader.result, {
                            alt: file.name,
                            style: 'max-width: 100%; height: auto;'
                        });
                    };
                    reader.readAsDataURL(file);
                };
                input.click();
            }
        }
    """,
    'setup': """
        function (editor) {
            // Auto-wrap tables in a responsive container
            editor.on('SetContent', function (e) {
                if (e.content && e.content.indexOf('<table') !== -1) {
                    var content = editor.getContent();
                    var updatedContent = content.replace(/<table(?![^>]*class="[^"]*table-responsive[^"]*")[^>]*>/g,
                        '<div class="table-wrapper"><table $1>');
                    updatedContent = updatedContent.replace(/<\/table>/g, '</table></div>');
                    if (content !== updatedContent) {
                        editor.setContent(updatedContent);
                    }
                }
            });

            // Handle paste events to ensure images are responsive
            editor.on('PastePostProcess', function (e) {
                var imgs = e.node.getElementsByTagName('img');
                for (var i = 0; i < imgs.length; i++) {
                    if (!imgs[i].hasAttribute('class') || imgs[i].getAttribute('class').indexOf('img-fluid') === -1) {
                        imgs[i].setAttribute('class', (imgs[i].getAttribute('class') || '') + ' img-fluid');
                    }
                    imgs[i].setAttribute('style', 'max-width: 100%; height: auto;');
                }

                // Handle tables in pasted content
                var tables = e.node.getElementsByTagName('table');
                for (var j = 0; j < tables.length; j++) {
                    if (!tables[j].parentNode.classList || !tables[j].parentNode.classList.contains('table-wrapper')) {
                        var wrapper = e.node.ownerDocument.createElement('div');
                        wrapper.className = 'table-wrapper';
                        tables[j].parentNode.insertBefore(wrapper, tables[j]);
                        wrapper.appendChild(tables[j]);
                    }

                    if (!tables[j].hasAttribute('class') || tables[j].getAttribute('class').indexOf('table') === -1) {
                        tables[j].setAttribute('class', (tables[j].getAttribute('class') || '') + ' table table-bordered');
                    }
                }
            });
        }
    """,
}

# Ensure TinyMCE URLs are included for upload handling
TINYMCE_EXTRA_MEDIA = {
    'css': {
        'all': [],
    },
    'js': [],
}


# Django-impersonate settings
IMPERSONATE_ALLOW_SUPERUSER = True
IMPERSONATE_REQUIRE_SUPERUSER = True
IMPERSONATE_REDIRECT_URL = '/'  # Redirect to homepage after starting impersonation
IMPERSONATE_USE_HTTP_REFERER = True  # Try to redirect back to the referring page
IMPERSONATE_CUSTOM_USER_QUERYSET = 'accounts.utils.get_impersonation_users'  # Optional: custom queryset for user lookup
IMPERSONATE_CUSTOM_ALLOW = 'accounts.utils.allow_impersonation'  # Optional: custom permission check
IMPERSONATE_DISABLE_LOGGING = False  # Enable logging of impersonation actions


