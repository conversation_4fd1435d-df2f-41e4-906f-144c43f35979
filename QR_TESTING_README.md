# QR Code Testing Suite

This directory contains comprehensive testing tools for the QR code generation functionality in `utils/qr_generator.py`.

## Test Files

### 1. `quick_test_qr.py` - Quick Basic Test
- **Purpose**: Fast validation of core QR code generation
- **Features**:
  - Generates a single QR code with letter "A"
  - Validates dimensions and file output
  - Shows generation time and file size
  - Optional testing of multiple letters
  - Enhanced with validation checks

**Usage:**
```bash
python quick_test_qr.py
```

**Output Files:**
- `quick_test_qr_final.png` - Main test QR code
- `quick_test_letter_*.png` - Additional letter tests (if selected)

### 2. `comprehensive_qr_test.py` - Full Test Suite
- **Purpose**: Comprehensive testing of all QR generation features
- **Features**:
  - Basic QR code generation
  - Multiple letter/character testing
  - Font loading and fallback testing
  - Dimension and positioning validation
  - Django ContentFile generation testing
  - Error handling with invalid inputs
  - Automatic cleanup of test files

**Usage:**
```bash
python comprehensive_qr_test.py
```

**Test Categories:**
1. **Basic QR Generation** - Core functionality
2. **Different Letters** - Tests A, B, C, X, Z, 1, 2, @
3. **Font Loading** - Tests Arial Black, Arial, Impact, and fallbacks
4. **Dimensions & Positioning** - Validates QR code size and letter placement
5. **Content File Generation** - Tests Django model integration
6. **Error Handling** - Tests with invalid inputs

### 3. `run_qr_tests.py` - Test Runner
- **Purpose**: Menu-driven interface to run tests
- **Features**:
  - Interactive menu system
  - Run individual tests or both
  - Clear result reporting
  - Error handling

**Usage:**
```bash
python run_qr_tests.py
```

## QR Code Specifications Tested

The tests validate the following QR code specifications:

### Technical Parameters
- **Version**: 1 (21x21 modules)
- **Error Correction**: High (H level)
- **Box Size**: 10 pixels per module
- **Border**: 4 modules
- **Expected Dimensions**: ~290x290 pixels

### Letter Design
- **Black Letter A**: 35% of QR code minimum dimension
- **White Background**: 10% larger than black letter
- **Positioning**: Centered with vertical offset of -50 pixels
- **Font**: Arial Black (with fallbacks)

### Visual Requirements
- Black letter A with straight lines (no V-shapes)
- Clean white background protection
- Perfect centering
- No QR code interference

## Running Tests

### Quick Start
```bash
# Run the basic test
python quick_test_qr.py

# Run comprehensive tests
python comprehensive_qr_test.py

# Use the menu system
python run_qr_tests.py
```

### Expected Results
- **All tests should pass** if QR generation is working correctly
- **Generated QR codes should be scannable** and lead to the test URLs
- **Visual inspection** should show clean, centered letter A designs

## Troubleshooting

### Common Issues

1. **Font Loading Failures**
   - Tests will fall back to custom letter generation
   - Check font availability on your system
   - Custom letters should still look good

2. **Django Setup Errors**
   - Ensure Django environment is properly configured
   - Check `DJANGO_SETTINGS_MODULE` setting
   - Run from the project root directory

3. **File Permission Errors**
   - Ensure write permissions in the test directory
   - Check disk space availability

4. **Import Errors**
   - Verify `utils/qr_generator.py` exists
   - Check Python path and virtual environment

### Test File Cleanup

The comprehensive test includes automatic cleanup, but you can manually remove test files:

```bash
# Remove all test QR code files
rm test_*.png quick_test_*.png
```

## Integration with Development

### Before Deployment
1. Run comprehensive tests to ensure all functionality works
2. Visually inspect generated QR codes
3. Test QR code scanning with mobile devices
4. Verify no regressions in existing functionality

### After Code Changes
1. Run quick test for immediate feedback
2. Run comprehensive test before committing changes
3. Check that custom letter generation still works when fonts fail

## Test Coverage

The test suite covers:
- ✅ Core QR generation functionality
- ✅ Font loading and fallback mechanisms
- ✅ Custom letter generation when fonts fail
- ✅ Multiple letter/character support
- ✅ Dimension calculations and positioning
- ✅ Django model integration
- ✅ Error handling and edge cases
- ✅ File I/O operations
- ✅ Performance timing

## Contributing

When adding new QR generation features:
1. Add corresponding tests to `comprehensive_qr_test.py`
2. Update this README with new test descriptions
3. Ensure all existing tests still pass
4. Add visual validation steps if needed
