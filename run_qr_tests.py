#!/usr/bin/env python
"""
QR Code Test Runner

This script provides a menu to run different QR code tests:
1. Quick test (basic functionality)
2. Comprehensive test suite (all features)
3. Both tests

Usage:
    python run_qr_tests.py
"""

import os
import sys
import subprocess

def run_quick_test():
    """Run the quick QR code test"""
    print("🚀 Running Quick QR Code Test...")
    print("=" * 50)
    
    try:
        result = subprocess.run([sys.executable, "quick_test_qr.py"], 
                              capture_output=False, text=True)
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Error running quick test: {e}")
        return False

def run_comprehensive_test():
    """Run the comprehensive QR code test suite"""
    print("🚀 Running Comprehensive QR Code Test Suite...")
    print("=" * 50)
    
    try:
        result = subprocess.run([sys.executable, "comprehensive_qr_test.py"], 
                              capture_output=False, text=True)
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Error running comprehensive test: {e}")
        return False

def show_menu():
    """Display the test menu"""
    print("\n🔧 QR Code Test Runner")
    print("=" * 30)
    print("1. Quick Test (basic functionality)")
    print("2. Comprehensive Test Suite (all features)")
    print("3. Run Both Tests")
    print("4. Exit")
    print("=" * 30)

def main():
    """Main test runner"""
    print("🎯 QR Code Testing Suite")
    print("Choose which tests to run:")
    
    while True:
        show_menu()
        
        try:
            choice = input("\nEnter your choice (1-4): ").strip()
            
            if choice == "1":
                print("\n" + "="*60)
                success = run_quick_test()
                if success:
                    print("\n✅ Quick test completed successfully!")
                else:
                    print("\n❌ Quick test failed!")
                    
            elif choice == "2":
                print("\n" + "="*60)
                success = run_comprehensive_test()
                if success:
                    print("\n✅ Comprehensive test completed successfully!")
                else:
                    print("\n❌ Comprehensive test failed!")
                    
            elif choice == "3":
                print("\n" + "="*60)
                print("Running both test suites...")
                
                quick_success = run_quick_test()
                print("\n" + "-"*60)
                comprehensive_success = run_comprehensive_test()
                
                print("\n" + "="*60)
                print("FINAL RESULTS:")
                print(f"Quick Test: {'✅ PASS' if quick_success else '❌ FAIL'}")
                print(f"Comprehensive Test: {'✅ PASS' if comprehensive_success else '❌ FAIL'}")
                
                if quick_success and comprehensive_success:
                    print("🎉 All tests passed!")
                elif quick_success or comprehensive_success:
                    print("⚠️  Some tests passed, some failed.")
                else:
                    print("❌ All tests failed.")
                    
            elif choice == "4":
                print("👋 Goodbye!")
                break
                
            else:
                print("❌ Invalid choice. Please enter 1, 2, 3, or 4.")
                
        except KeyboardInterrupt:
            print("\n\n👋 Test runner interrupted. Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
