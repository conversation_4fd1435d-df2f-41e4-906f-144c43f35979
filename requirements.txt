# Django and Core Dependencies
Django
django-debug-toolbar
django-widget-tweaks
django-formtools  # For wizard views
django-crispy-forms  # For form rendering
crispy-bootstrap5  # Template pack for crispy-forms
Pillow  # For image handling
django-tinymce  # For rich text editing
pytz  # For timezone support

# Authentication and Permissions
django-guardian  # For object-level permissions
django-impersonate  # For user impersonation
django-axes  # For login attempt tracking

# Security
django-csp  # For Content Security Policy headers
django-cors-headers  # For CORS support

# AI/LLM Integration
openai  # OpenAI API client
tiktoken  # For token counting with OpenAI models
anthropic  # Anthropic API client for Claude models
httpx  # HTTP client library (required by newer OpenAI client)
google-generativeai  # Google's Gemini API client

# File Processing
python-magic  # For file type detection
PyPDF2  # For PDF processing
python-docx  # For Word document processing
markdown  # For Markdown to HTML conversion

# QR Code Generation
qrcode  # For generating QR codes
pypng  # Dependency for qrcode's default image format

# Environment and Configuration
python-decouple  # For configuration management
python-dotenv  # For loading .env files

# Web Server
gunicorn  # WSGI HTTP server
whitenoise  # For static file serving

# Testing and Development
coverage  # For test coverage reporting
pytest  # Testing framework
pytest-django  # Django plugin for pytest
factory-boy  # For test data generation
flake8  # For code style checking
black  # For code formatting

# Database
psycopg2-binary  # For PostgreSQL
mysqlclient  # For MySQL (required for cPanel)

# Caching and Task Queue (Uncomment as needed)
# redis  # For caching
# celery  # For background tasks

# Storage (Uncomment as needed)
# django-storages  # For cloud storage
# boto3  # For AWS S3
